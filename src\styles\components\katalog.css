/* ===== CATALOG PAGE LAYOUT ===== */

/* Main layout container - ensures proper page structure */
.catalog-main-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Content wrapper - grows to fill available space */
.catalog-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Main container for catalog content */
.catalog-container {
    display: flex;
    max-width: 1300px;
    margin: 0 auto;
    width: 100%;
    padding: 20px;
    gap: 24px;
    align-items: flex-start;
    flex: 1;
}

/* ===== FILTER SIDEBAR ===== */

.catalog-filter {
    width: 280px;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    position: sticky;
    top: 20px;
    flex-shrink: 0;
    height: fit-content;
}

.catalog-filter__title {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.catalog-filter__group {
    margin-bottom: 24px;
}

.catalog-filter__label {
    display: block;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #34495e;
}

.catalog-filter__input-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.catalog-filter__input-prefix {
    width: 50px;
    font-size: 11px;
    font-weight: 500;
    background-color: #f8f9fa;
    padding: 10px 8px;
    border: 1px solid #e9ecef;
    border-right: none;
    border-radius: 6px 0 0 6px;
    text-align: center;
    color: #6c757d;
}

.catalog-filter__input {
    flex: 1;
    padding: 10px 12px;
    font-size: 14px;
    color: #495057;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 0 6px 6px 0;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.catalog-filter__input:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.catalog-filter__select {
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    color: #495057;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    margin-bottom: 8px;
}

.catalog-filter__select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ===== MAIN CONTENT AREA ===== */
.catalog-main-content {
    flex: 1;
    min-width: 0;
    width: 100%;
    margin: 0 0 40px 0;
}

.catalog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.catalog-header__count {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #2c3e50;
}

/* ===== PRODUCT GRID ===== */

.catalog-product-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    width: 100%;
    margin-bottom: 40px;
}

/* ===== LOADING INDICATOR ===== */

.catalog-loading {
    text-align: center;
    padding: 24px;
    color: #6c757d;
    font-size: 14px;
    transition: opacity 0.2s ease-in-out;
}

.catalog-loading.hidden {
    display: none;
}

.catalog-loading i {
    margin-right: 8px;
}

/* ===== PRODUCT CARDS ===== */
.catalog-product-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    cursor: pointer;
    overflow: hidden;
    will-change: transform;
    contain: layout style paint;
}

.catalog-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.catalog-product-card__image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 0;
}

.catalog-product-card__content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

.catalog-product-card__name {
    font-weight: 500;
    font-size: 14px;
    margin: 0 0 8px 0;
    color: #2c3e50;
    line-height: 1.4;
    display: -webkit-box;
    --webkit-line-clamp: 2;
    --webkit-box-orient: vertical;
    overflow: hidden;
    position: relative;
}

.catalog-product-card__price {
    font-weight: 600;
    font-size: 16px;
    margin: 0 0 8px 0;
    color: #2c3e50;
}

/* ===== PROMO STYLES ===== */

.promo-badge {
    background: #dc2626;
    color: white;
    font-size: 8px;
    padding: 2px 6px;
    border-radius: 5px;
    font-weight: 600;
    margin-left: 6px;
    vertical-align: top;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.catalog-product-card__price-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 0 0 8px 0;
}

.catalog-product-card__price-original {
    font-size: 12px;
    color: #6b7280;
    text-decoration: line-through;
    margin: 0;
    font-weight: 400;
}

.catalog-product-card__price-promo {
    font-weight: 600;
    font-size: 16px;
    margin: 0;
    color: #dc2626;
}

.catalog-product-card__discount {
    background: #dc2626;
    color: white;
    font-size: 9px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
    align-self: flex-start;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.catalog-product-card__rating {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 8px;
}

.catalog-product-card__rating i {
    color: #FFD700;
    margin-right: 4px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (min-width: 400px) {
    footer {
        display: none;
    }
}


/* Desktop - 5 columns at 1024px and above */
@media (min-width: 1024px) {
    .catalog-product-grid {
        grid-template-columns: repeat(4 , 1fr);
    }
}
@media (min-width: 1280px) {
    .catalog-product-grid {
        grid-template-columns: repeat(5 , 1fr);
    }
}

/* Large tablet - 4 columns */
@media (max-width: 1023px) and (min-width: 769px) {
    .catalog-product-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Small tablet - 3 columns */
@media (max-width: 768px) and (min-width: 577px) {
    .catalog-product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
    }
}

/* Mobile - 2 columns */
@media (max-width: 576px) {
    .catalog-product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

/* ===== TABLET AND MOBILE LAYOUT ===== */

/* Tablet and below - filter becomes horizontal */
@media (max-width: 1023px) {
    .catalog-container {
        flex-direction: column;
        gap: 16px;
        padding: 16px;
    }

    .catalog-filter {
        position: static;
        width: 100%;
        padding: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        border-radius: 8px;
    }

    .catalog-filter__title {
        width: 100%;
        margin-bottom: 12px;
        font-size: 16px;
    }

    .catalog-filter__group {
        flex: 1;
        min-width: 200px;
        margin-bottom: 0;
    }

    .catalog-filter__group:last-child {
        flex: 2;
        min-width: 280px;
    }

    .catalog-header {
        margin-bottom: 16px;
    }

    .catalog-header__count {
        font-size: 15px;
    }
}

/* ===== MOBILE SPECIFIC STYLES ===== */

@media (max-width: 576px) {
    .catalog-container {
        padding: 12px;
    }

    .catalog-filter {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .catalog-filter__group,
    .catalog-filter__group:last-child {
        min-width: 100%;
        flex: none;
    }

    .catalog-filter__title {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .catalog-filter__label {
        font-size: 13px;
        margin-bottom: 8px;
    }

    .catalog-filter__input,
    .catalog-filter__select {
        padding: 8px 10px;
        font-size: 13px;
    }

    .catalog-filter__input-prefix {
        padding: 8px 6px;
        font-size: 10px;
    }

    .catalog-header__count {
        font-size: 14px;
    }

    .catalog-product-card__image {
        height: 140px;
    }

    .catalog-product-card__content {
        padding: 12px;
    }

    .catalog-product-card__name {
        font-size: 13px;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }

    .catalog-product-card__price {
        font-size: 14px;
    }

    .catalog-product-card__price-promo {
        font-size: 14px;
    }

    .catalog-product-card__price-original {
        font-size: 11px;
    }

    .catalog-product-card__discount {
        font-size: 8px;
        padding: 1px 4px;
    }

    .promo-badge {
        font-size: 7px;
        padding: 1px 4px;
    }

    .catalog-product-card__rating {
        font-size: 11px;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Disable smooth scroll for better infinite scroll performance */
html {
    scroll-behavior: auto;
}

/* Optimize for animations and rendering */
.catalog-product-card {
    transform: translateZ(0);
    backface-visibility: hidden;
}